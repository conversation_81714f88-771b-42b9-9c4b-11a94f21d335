"""
Quality Control

This module provides quality control functionality for the RAG system.
"""

from typing import Dict, List, Any, Optional
from ...utils.logging import get_logger
from ...utils.timeout_retry import hybrid_search_operation
from .base import BaseQualityComponent

logger = get_logger(__name__)

class QualityControl(BaseQualityComponent):
    """
    Quality control component for validating and improving search results.
    """
    
    def __init__(
        self,
        llm_adapter: Any,
        min_threshold: float = 0.7,
        weights: Optional[Dict[str, float]] = None
    ):
        """
        Initialize quality control.
        
        Args:
            llm_adapter: LLM adapter for scoring
            min_threshold: Minimum quality score threshold
            weights: Optional weights for different quality dimensions
        """
        self.llm_adapter = llm_adapter
        self.min_threshold = min_threshold
        self.weights = weights or {
            "semantic": 0.6,
            "source": 0.4
        }

    @hybrid_search_operation
    async def validate_results(
        self,
        results: List[Dict[str, Any]],
        query: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Validate results through essential dimensions:
        1. Semantic relevance
        2. Source reliability
        3. Basic consistency check
        """
        try:
            validated_results = []
            
            for result in results:
                # Score each dimension
                semantic_score = await self._score_semantic_relevance(
                    result,
                    query
                )
                source_score = await self._score_source_reliability(result)
                
                # Combine scores
                scores = {
                    "semantic": semantic_score,
                    "source": source_score
                }
                
                total_score = await self._combine_scores(scores, self.weights)
                
                if total_score >= self.min_threshold:
                    # Basic consistency check
                    if await self._check_basic_consistency(result, validated_results):
                        result["quality_scores"] = {
                            **scores,
                            "total": total_score
                        }
                        validated_results.append(result)
            
            return validated_results
            
        except Exception as e:
            logger.error(f"Error validating results: {str(e)}")
            raise

    @hybrid_search_operation
    async def _score_semantic_relevance(
        self,
        result: Dict[str, Any],
        query: Dict[str, Any]
    ) -> float:
        """Score semantic relevance of result to query."""
        try:
            prompt = f"""
            Score the semantic relevance of this result to the query:
            
            Query: {query.get('text', '')}
            
            Result: {result.get('text', '')}
            
            Return a score between 0 and 1 where:
            0 = Completely irrelevant
            1 = Perfectly relevant
            """
            
            response = await self.llm_adapter.generate(prompt)
            return float(response)
            
        except Exception as e:
            logger.error(f"Error scoring semantic relevance: {str(e)}")
            return 0.0

    @hybrid_search_operation
    async def _score_source_reliability(self, result: Dict[str, Any]) -> float:
        """Score source reliability of result."""
        try:
            prompt = f"""
            Score the reliability of this source:
            
            Source: {result.get('source', 'Unknown')}
            Metadata: {result.get('metadata', {})}
            
            Return a score between 0 and 1 where:
            0 = Very unreliable
            1 = Very reliable
            """
            
            response = await self.llm_adapter.generate(prompt)
            return float(response)
            
        except Exception as e:
            logger.error(f"Error scoring source reliability: {str(e)}")
            return 0.0

    @hybrid_search_operation
    async def _combine_scores(
        self,
        scores: Dict[str, float],
        weights: Dict[str, float]
    ) -> float:
        """Combine scores using weighted average."""
        try:
            total_score = 0.0
            total_weight = 0.0
            
            for dimension, score in scores.items():
                weight = weights.get(dimension, 0.0)
                total_score += score * weight
                total_weight += weight
            
            return total_score / total_weight if total_weight > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Error combining scores: {str(e)}")
            return 0.0

    @hybrid_search_operation
    async def _check_basic_consistency(
        self,
        result: Dict[str, Any],
        validated_results: List[Dict[str, Any]]
    ) -> bool:
        """Check basic consistency of result with validated results."""
        try:
            # Check for exact duplicates
            for validated in validated_results:
                if result.get("text") == validated.get("text"):
                    return False
            
            # Check for high similarity
            for validated in validated_results:
                similarity = await self._calculate_similarity(
                    result.get("text", ""),
                    validated.get("text", "")
                )
                if similarity > 0.9:  # High similarity threshold
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking consistency: {str(e)}")
            return True  # Allow result if check fails

    @hybrid_search_operation
    async def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two texts."""
        try:
            prompt = f"""
            Calculate the similarity between these two texts:
            
            Text 1: {text1}
            
            Text 2: {text2}
            
            Return a score between 0 and 1 where:
            0 = Completely different
            1 = Identical
            """
            
            response = await self.llm_adapter.generate(prompt)
            return float(response)
            
        except Exception as e:
            logger.error(f"Error calculating similarity: {str(e)}")
            return 0.0 