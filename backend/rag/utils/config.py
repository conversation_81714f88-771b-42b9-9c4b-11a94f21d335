"""
Configuration Utilities

This module provides configuration utilities for the RAG system.
"""

import logging
from typing import Dict, Any, Optional

from ...app.core.types import Document, SearchResult
from ...app.config import get_settings
from ..embeddings import get_embedding_model, EmbeddingModel
from ..vector_store import get_vector_store, VectorStore

logger = logging.getLogger(__name__)


class RAGConfig:
    """Configuration container for RAG components."""

    def __init__(self, **kwargs):
        """
        Initialize RAG configuration.

        Args:
            **kwargs: Configuration options
        """
        # Load from settings
        settings = get_settings()

        # Embedding configuration
        self.embedding_provider = kwargs.get("embedding_provider", settings.DEFAULT_EMBEDDING_MODEL)
        self.embedding_model = kwargs.get("embedding_model", settings.EMBEDDING_MODEL)
        self.embedding_dimension = kwargs.get("embedding_dimension", settings.VECTOR_DIMENSION)

        # Vector store configuration
        self.vector_store_type = kwargs.get("vector_store_type", settings.VECTOR_STORE_TYPE)
        self.vector_store_path = kwargs.get("vector_store_path", settings.VECTOR_STORE_PATH)
        self.vector_store_dimension = kwargs.get("vector_store_dimension", self.embedding_dimension)
        self.vector_store_table = kwargs.get("vector_store_table", "documents")
        self.vector_store_distance = kwargs.get("vector_store_distance", "cosine")

        # Retriever configuration
        self.retriever_type = kwargs.get("retriever_type", "hybrid")
        self.vector_weight = kwargs.get("vector_weight", 0.7)
        self.keyword_weight = kwargs.get("keyword_weight", 0.3)
        self.use_reranking = kwargs.get("use_reranking", False)

        # Context window configuration
        self.max_tokens = kwargs.get("max_tokens", 4000)
        self.token_buffer = kwargs.get("token_buffer", 1000)

        # Timeout configuration
        self.embedding_timeout = kwargs.get("embedding_timeout", 5)
        self.vector_search_timeout = kwargs.get("vector_search_timeout", 10)
        self.keyword_search_timeout = kwargs.get("keyword_search_timeout", 8)
        self.hybrid_search_timeout = kwargs.get("hybrid_search_timeout", 12)

        # Additional options
        self.additional_options = kwargs


async def initialize_embedding_model(config: Optional[RAGConfig] = None) -> EmbeddingModel:
    """
    Initialize an embedding model.

    Args:
        config: RAG configuration

    Returns:
        Initialized embedding model
    """
    if config is None:
        config = RAGConfig()

    logger.info(f"Initializing embedding model: {config.embedding_model}")
    return get_embedding_model(
        provider=config.embedding_provider,
        model=config.embedding_model,
        dimension=config.embedding_dimension
    )


async def initialize_knowledge_base_service(
    embedding_model: Optional[EmbeddingModel] = None,
    vector_store: Optional[VectorStore] = None,
    config: Optional[RAGConfig] = None
):
    """
    Initialize a knowledge base service.

    Args:
        embedding_model: Optional embedding model
        vector_store: Optional vector store
        config: RAG configuration

    Returns:
        Initialized knowledge base service
    """
    if config is None:
        config = RAGConfig()

    # Import here to avoid circular imports
    from ..knowledge_base import KnowledgeBaseService

    # Initialize embedding model if not provided
    if embedding_model is None:
        embedding_model = await initialize_embedding_model(config)

    # Initialize vector store if not provided
    if vector_store is None:
        vector_store = await get_vector_store(
            store_type=config.vector_store_type,
            dimension=config.vector_store_dimension,
            table_name=config.vector_store_table,
            distance_metric=config.vector_store_distance
        )

    logger.info("Initializing knowledge base service")

    return KnowledgeBaseService(
        vector_store=vector_store,
        embedding_model=embedding_model,
        vector_weight=config.vector_weight,
        keyword_weight=config.keyword_weight,
        use_reranking=config.use_reranking
    ) 