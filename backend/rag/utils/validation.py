"""
Validation Utilities

This module provides validation utilities for the RAG system.
"""

import logging
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, validator

logger = logging.getLogger(__name__)


class DocumentValidation(BaseModel):
    """Document validation model."""
    
    content: str = Field(..., min_length=1)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    @validator('content')
    def validate_content(cls, v):
        """Validate document content."""
        if not v.strip():
            raise ValueError("Document content cannot be empty")
        return v


class QueryValidation(BaseModel):
    """Query validation model."""
    
    text: str = Field(..., min_length=1)
    filters: Optional[Dict[str, Any]] = Field(default=None)
    
    @validator('text')
    def validate_text(cls, v):
        """Validate query text."""
        if not v.strip():
            raise ValueError("Query text cannot be empty")
        return v


def validate_document(document: Dict[str, Any]) -> bool:
    """
    Validates a document using Pydantic model.
    
    Args:
        document: Document dictionary to validate
        
    Returns:
        True if document is valid, False otherwise
    """
    try:
        DocumentValidation(**document)
        return True
    except Exception as e:
        logger.error(f"Document validation failed: {e}")
        return False


def validate_query(query: Dict[str, Any]) -> bool:
    """
    Validates a query using Pydantic model.
    
    Args:
        query: Query dictionary to validate
        
    Returns:
        True if query is valid, False otherwise
    """
    try:
        QueryValidation(**query)
        return True
    except Exception as e:
        logger.error(f"Query validation failed: {e}")
        return False

def validate_config(config: Dict[str, Any]) -> bool:
    """
    Validates the RAG configuration.
    
    Args:
        config: Configuration dictionary to validate
        
    Returns:
        True if configuration is valid, False otherwise
    """
    required_fields = [
        "embedding_model",
        "vector_store",
        "retriever",
        "context_window",
        "timeouts"
    ]
    
    for field in required_fields:
        if field not in config:
            logger.error(f"Missing required configuration field: {field}")
            return False
            
    # Validate timeouts
    timeouts = config["timeouts"]
    required_timeouts = ["embedding", "vector_search", "hybrid_search", "knowledge_base"]
    
    for timeout in required_timeouts:
        if timeout not in timeouts:
            logger.error(f"Missing required timeout configuration: {timeout}")
            return False
        if not isinstance(timeouts[timeout], (int, float)) or timeouts[timeout] <= 0:
            logger.error(f"Invalid timeout value for {timeout}: {timeouts[timeout]}")
            return False
            
    return True 