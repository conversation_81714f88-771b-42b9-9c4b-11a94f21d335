"""
Prompt Generator

This module provides essential prompt templates for the RAG system.
Focuses on core functionality for query processing and quality control.
"""

from typing import Dict, Any, List, Optional
from ...utils.logging import get_logger

logger = get_logger(__name__)

class PromptGenerator:
    """
    Generates prompts for various RAG operations.
    Focuses on essential templates for core functionality.
    """
    
    def generate_query_prompt(self, query: str) -> str:
        """Generate prompt for query processing."""
        return f"""Process this query to improve retrieval:

Query: {query}

Provide a clear and specific version that:
1. Maintains the original intent
2. Uses precise terminology
3. Is unambiguous

Processed query:"""
    
    def generate_scoring_prompt(
        self,
        content: str,
        context: Dict[str, Any],
        scoring_type: str
    ) -> str:
        """Generate prompt for document scoring."""
        if scoring_type == "relevance":
            return f"""Score the relevance of this document:

Document: {content}

Context: {context.get('query', 'No query provided')}

Provide a relevance score between 0 and 1, where:
- 1.0 means highly relevant
- 0.0 means not relevant

Score:"""
        
        elif scoring_type == "reliability":
            return f"""Score the reliability of this document:

Document: {content}

Source: {context.get('source', 'Unknown')}

Provide a reliability score between 0 and 1, where:
- 1.0 means highly reliable
- 0.0 means not reliable

Score:"""
        
        else:
            return f"""Score this document:

Document: {content}

Context: {context}

Provide a score between 0 and 1.

Score:"""
    
    def generate_quality_prompt(
        self,
        query: str,
        results: List[Dict[str, Any]]
    ) -> str:
        """Generate prompt for quality control."""
        return f"""Evaluate the quality of these search results:

Query: {query}

Results:
{self._format_results(results)}

Evaluate the results based on:
1. Relevance to the query
2. Reliability of sources
3. Overall quality

Provide a quality score between 0 and 1.

Score:"""
    
    def _format_results(self, results: List[Dict[str, Any]]) -> str:
        """Format results for prompt generation."""
        formatted = []
        for i, result in enumerate(results, 1):
            formatted.append(f"Result {i}:")
            formatted.append(f"Content: {result.get('text', 'No content')}")
            formatted.append(f"Source: {result.get('source', 'Unknown')}")
            formatted.append(f"Score: {result.get('score', 0.0)}")
            formatted.append("")
        
        return "\n".join(formatted) 