"""
RAG (Retrieval-Augmented Generation) package.

This package provides components for document retrieval and generation:
- Vector stores for document storage and retrieval
- Embedding models for text vectorization
- Retrievers for document search
- Knowledge base for document management
"""

from .config import rag_settings, RAGSettings
from .vector_store import VectorStore, PgVectorStore, get_vector_store
from .embeddings import EmbeddingModel, OpenAIEmbedding, get_embedding_model
from .retriever import Retriever, HybridRetriever, get_retriever
from .knowledge_base import KnowledgeBaseService

__all__ = [
    # Configuration
    "rag_settings",
    "RAGSettings",
    
    # Vector stores
    "VectorStore",
    "PgVectorStore",
    "get_vector_store",
    
    # Embedding models
    "EmbeddingModel",
    "OpenAIEmbedding",
    "get_embedding_model",
    
    # Retrievers
    "Retriever",
    "HybridRetriever",
    "get_retriever",
    
    # Knowledge base
    "KnowledgeBaseService",
]
