"""
Knowledge Base Service

This module provides the knowledge base service for managing documents and search.
"""

from typing import Dict, List, Any, Optional
from ...utils.logging import get_logger
from ...utils.timeout_retry import knowledge_base_operation
from ...app.core.types import Document, SearchResult
from ...app.core.metrics import metrics
from ...app.core.db.database import get_db_context
from ...app.core.db.models import DBDocument
from .vector_store import VectorStore
from .embeddings import EmbeddingModel
from .llm import RAGLLMAdapter
from .retriever import Retriever

logger = get_logger(__name__)

class KnowledgeBaseService:
    """
    Service for managing the knowledge base.
    
    This service provides a high-level interface for:
    1. Document management (add, update, delete, get)
    2. Document search
    3. Knowledge base maintenance
    """
    
    def __init__(
        self,
        vector_store: Optional[VectorStore] = None,
        embedding_model: Optional[EmbeddingModel] = None,
        llm_adapter: Optional[RAGLLMAdapter] = None,
        vector_weight: Optional[float] = None,
        keyword_weight: Optional[float] = None,
        use_reranking: Optional[bool] = None
    ):
        """
        Initialize the knowledge base service.
        
        Args:
            vector_store: Vector store for document storage
            embedding_model: Model for generating embeddings
            llm_adapter: LLM adapter for text generation
            vector_weight: Weight for vector similarity scores
            keyword_weight: Weight for keyword match scores
            use_reranking: Whether to use reranking
        """
        self.vector_store = vector_store
        self.embedding_model = embedding_model
        self.llm_adapter = llm_adapter
        self.vector_weight = vector_weight
        self.keyword_weight = keyword_weight
        self.use_reranking = use_reranking
        
        # Initialize retriever
        self.retriever = Retriever(
            vector_store=vector_store,
            embedding_model=embedding_model,
            vector_weight=vector_weight,
            keyword_weight=keyword_weight,
            use_reranking=use_reranking
        )

    @knowledge_base_operation
    async def add_document(
        self,
        document: Document,
        **kwargs
    ) -> str:
        """
        Add a document to the knowledge base.
        
        Args:
            document: Document to add
            **kwargs: Additional arguments
            
        Returns:
            Document ID
        """
        try:
            # Add to vector store
            doc_id = await self.vector_store.add_document(document, **kwargs)
            
            # Add to database
            with get_db_context() as db:
                db_doc = DBDocument(
                    id=doc_id,
                    title=document["metadata"].get("title", "Untitled"),
                    source=document["metadata"].get("source"),
                    doc_metadata=document["metadata"]
                )
                db.add(db_doc)
                db.commit()
            
            metrics.record_success("knowledge_base_add")
            return doc_id
            
        except Exception as e:
            metrics.record_failure("knowledge_base_add", str(e))
            logger.error(f"Error adding document: {str(e)}")
            raise

    @knowledge_base_operation
    async def update_document(
        self,
        doc_id: str,
        document: Document,
        **kwargs
    ) -> None:
        """
        Update a document in the knowledge base.
        
        Args:
            doc_id: Document ID
            document: Updated document
            **kwargs: Additional arguments
        """
        try:
            # Update in vector store
            await self.vector_store.update_document(doc_id, document, **kwargs)
            
            # Update in database
            with get_db_context() as db:
                db_doc = db.query(DBDocument).filter(DBDocument.id == doc_id).first()
                if db_doc:
                    db_doc.title = document["metadata"].get("title", "Untitled")
                    db_doc.source = document["metadata"].get("source")
                    db_doc.doc_metadata = document["metadata"]
                    db.commit()
            
            metrics.record_success("knowledge_base_update")
            
        except Exception as e:
            metrics.record_failure("knowledge_base_update", str(e))
            logger.error(f"Error updating document: {str(e)}")
            raise

    @knowledge_base_operation
    async def delete_document(
        self,
        doc_id: str
    ) -> None:
        """
        Delete a document from the knowledge base.
        
        Args:
            doc_id: Document ID
        """
        try:
            # Delete from vector store
            await self.vector_store.delete_document(doc_id)
            
            # Delete from database
            with get_db_context() as db:
                db.query(DBDocument).filter(DBDocument.id == doc_id).delete()
                db.commit()
            
            metrics.record_success("knowledge_base_delete")
            
        except Exception as e:
            metrics.record_failure("knowledge_base_delete", str(e))
            logger.error(f"Error deleting document: {str(e)}")
            raise

    @knowledge_base_operation
    async def get_document(
        self,
        doc_id: str
    ) -> Optional[Document]:
        """
        Get a document from the knowledge base.
        
        Args:
            doc_id: Document ID
            
        Returns:
            Document if found, None otherwise
        """
        try:
            # Get from vector store
            document = await self.vector_store.get_document(doc_id)
            
            metrics.record_success("knowledge_base_get")
            return document
            
        except Exception as e:
            metrics.record_failure("knowledge_base_get", str(e))
            logger.error(f"Error getting document: {str(e)}")
            raise

    @knowledge_base_operation
    async def search(
        self,
        query: str,
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """
        Search the knowledge base.
        
        Args:
            query: Search query
            limit: Maximum number of results
            filters: Optional metadata filters
            
        Returns:
            List of search results with scores
        """
        try:
            # Use retriever for search
            results = await self.retriever.search(query, limit, filters)
            
            metrics.record_success("knowledge_base_search")
            return results
            
        except Exception as e:
            metrics.record_failure("knowledge_base_search", str(e))
            logger.error(f"Error searching knowledge base: {str(e)}")
            raise