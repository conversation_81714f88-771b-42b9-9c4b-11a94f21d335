"""
Common Types

This module provides common type definitions and runtime validation.
"""

from typing import Any, Dict, List, Optional, TypedDict, Union
from pydantic import BaseModel, Field, validator


class Document(BaseModel):
    """Document type for RAG system."""
    id: Optional[str] = None
    content: str
    embedding: Optional[List[float]] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class SearchResult(BaseModel):
    """Search result type for RAG system."""
    document: Document
    score: float
    source: str


class QueryResult(TypedDict):
    """Query result structure."""
    answer: str
    sources: List[SearchResult]
    confidence: float


class EmbeddingConfig(BaseModel):
    """Configuration for embedding models."""
    model_name: str = "text-embedding-3-small"
    dimension: int = 1536
    provider: str = "openai"
    
    @validator("provider")
    def validate_provider(cls, v: str) -> str:
        """Validate embedding provider."""
        allowed = ["huggingface", "openai"]
        if v.lower() not in allowed:
            raise ValueError(f"Provider must be one of {allowed}")
        return v.lower()


class VectorStoreConfig(BaseModel):
    """Configuration for vector stores."""
    type: str = "pgvector"
    path: Optional[str] = None
    dimension: int = 1536
    table_name: str = "documents"
    distance_metric: str = "cosine"
    
    @validator("type")
    def validate_type(cls, v: str) -> str:
        """Validate vector store type."""
        allowed = ["pgvector", "memory"]
        if v.lower() not in allowed:
            raise ValueError(f"Type must be one of {allowed}")
        return v.lower()


class RetrieverConfig(BaseModel):
    """Configuration for retrievers."""
    type: str = "hybrid"
    max_documents: int = 5
    min_score: float = 0.7
    vector_weight: float = 0.7
    keyword_weight: float = 0.3
    use_reranking: bool = False
    
    @validator("type")
    def validate_type(cls, v: str) -> str:
        """Validate retriever type."""
        allowed = ["hybrid", "vector", "keyword"]
        if v.lower() not in allowed:
            raise ValueError(f"Type must be one of {allowed}")
        return v.lower()
    
    @validator("min_score")
    def validate_score(cls, v: float) -> float:
        """Validate minimum score."""
        if not 0 <= v <= 1:
            raise ValueError("Score must be between 0 and 1")
        return v


class ContextConfig(BaseModel):
    """Configuration for context management."""
    max_tokens: int = 2000
    overlap: int = 200
    token_buffer: int = 1000
    
    @validator("max_tokens")
    def validate_max_tokens(cls, v: int) -> int:
        """Validate maximum tokens."""
        if v <= 0:
            raise ValueError("Maximum tokens must be positive")
        return v
    
    @validator("overlap")
    def validate_overlap(cls, v: int, values: Dict[str, Any]) -> int:
        """Validate overlap."""
        if v < 0:
            raise ValueError("Overlap must be non-negative")
        if "max_tokens" in values and v >= values["max_tokens"]:
            raise ValueError("Overlap must be less than maximum tokens")
        return v


def validate_document(doc: Dict[str, Any]) -> Document:
    """
    Validate a document structure.
    
    Args:
        doc: Document dictionary to validate
        
    Returns:
        Validated Document
        
    Raises:
        ValueError: If document is invalid
    """
    if not isinstance(doc, dict):
        raise ValueError("Document must be a dictionary")
        
    if "content" not in doc or not isinstance(doc["content"], str):
        raise ValueError("Document must have a string 'content' field")
        
    if "metadata" not in doc or not isinstance(doc["metadata"], dict):
        raise ValueError("Document must have a dictionary 'metadata' field")
        
    return cast(Document, doc)


def validate_search_result(result: Dict[str, Any]) -> SearchResult:
    """
    Validate a search result structure.
    
    Args:
        result: Search result dictionary to validate
        
    Returns:
        Validated SearchResult
        
    Raises:
        ValueError: If result is invalid
    """
    if not isinstance(result, dict):
        raise ValueError("Search result must be a dictionary")
        
    if "document" not in result:
        raise ValueError("Search result must have a 'document' field")
    validate_document(result["document"])
    
    if "score" not in result or not isinstance(result["score"], (int, float)):
        raise ValueError("Search result must have a numeric 'score' field")
        
    if "source" not in result or not isinstance(result["source"], str):
        raise ValueError("Search result must have a string 'source' field")
        
    return cast(SearchResult, result)


def validate_query_result(result: Dict[str, Any]) -> QueryResult:
    """
    Validate a query result structure.
    
    Args:
        result: Query result dictionary to validate
        
    Returns:
        Validated QueryResult
        
    Raises:
        ValueError: If result is invalid
    """
    if not isinstance(result, dict):
        raise ValueError("Query result must be a dictionary")
        
    if "answer" not in result or not isinstance(result["answer"], str):
        raise ValueError("Query result must have a string 'answer' field")
        
    if "sources" not in result or not isinstance(result["sources"], list):
        raise ValueError("Query result must have a list 'sources' field")
    for source in result["sources"]:
        validate_search_result(source)
        
    if "confidence" not in result or not isinstance(result["confidence"], (int, float)):
        raise ValueError("Query result must have a numeric 'confidence' field")
        
    return cast(QueryResult, result) 