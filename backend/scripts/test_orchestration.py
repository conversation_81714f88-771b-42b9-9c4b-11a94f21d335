#!/usr/bin/env python3
"""
End-to-end test script for the orchestration system.
Tests the entire flow from prompt to final response, including:
- RAG document retrieval
- Multi-agent orchestration
- Task execution and coordination
- Response generation
- Agent conversations and interactions
"""

import argparse
import asyncio
import logging
import json
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from dotenv import load_dotenv
import os

from backend.orchestration.graph import build_graph
from backend.orchestration.state import LangGraphState, OrchestrationStep
from backend.llm.base import LLMAdapter
from backend.llm.factory import get_llm_adapter
from backend.orchestration.db import *
from backend.orchestration.tracer import TraceLogger, TraceEvent

logger = logging.getLogger(__name__)

load_dotenv(dotenv_path=".env")
print("DEBUG: OPENAI_API_KEY =", os.getenv("OPENAI_API_KEY"))

class EnhancedTraceLogger(TraceLogger):
    """Enhanced tracer for detailed orchestration tracking."""
    
    def __init__(self):
        super().__init__()
        self.conversations = []
        self.agent_states = {}
        self.task_progress = {}
        self.current_task = None
        print("\n[Trace] Enhanced tracing enabled")
    
    def on_agent_interaction(self, from_agent: str, to_agent: str, message: str, context: Dict[str, Any]):
        """Track agent interactions and conversations."""
        interaction = {
            "timestamp": datetime.now().isoformat(),
            "from_agent": from_agent,
            "to_agent": to_agent,
            "message": message,
            "context": context
        }
        self.conversations.append(interaction)
        print(f"\n[Trace] Agent Interaction: {from_agent} -> {to_agent}")
        print(f"Message: {message}")
        if context:
            print(f"Context: {json.dumps(context, indent=2)}")
    
    def on_task_update(self, task_id: str, status: str, agent: str, details: Dict[str, Any]):
        """Track task progress and updates."""
        if task_id not in self.task_progress:
            self.task_progress[task_id] = []
            self.current_task = task_id
            print(f"\n[Trace] Starting new task: {task_id}")
        
        update = {
            "timestamp": datetime.now().isoformat(),
            "status": status,
            "agent": agent,
            "details": details
        }
        self.task_progress[task_id].append(update)
        
        # Print progress bar for task
        total_steps = 5  # Planning, Assignment, Execution, Review, Completion
        current_step = {
            "PLANNING": 1,
            "ASSIGNMENT": 2,
            "EXECUTION": 3,
            "REVIEW": 4,
            "COMPLETED": 5
        }.get(status, 0)
        
        progress = "=" * current_step + "-" * (total_steps - current_step)
        print(f"\n[Trace] Task Progress: [{progress}] {status}")
        print(f"Agent: {agent}")
        if details:
            print(f"Details: {json.dumps(details, indent=2)}")
    
    def on_agent_state_change(self, agent: str, state: Dict[str, Any]):
        """Track agent state changes."""
        if agent not in self.agent_states:
            self.agent_states[agent] = []
            print(f"\n[Trace] Initializing agent: {agent}")
        
        state_update = {
            "timestamp": datetime.now().isoformat(),
            "state": state
        }
        self.agent_states[agent].append(state_update)
        print(f"\n[Trace] Agent State Change: {agent}")
        print(f"New State: {json.dumps(state, indent=2)}")

async def run_orchestration(prompt: str, llm_model: str = "openai", visualize: bool = True, trace: bool = True) -> None:
    print("\n=== Starting Orchestration System ===")
    print(f"Prompt: {prompt}")
    print(f"LLM Model: {llm_model}")
    print("=" * 40)
    import sys; sys.stdout.flush()
    
    """
    Run the orchestration system with the given prompt.
    
    Args:
        prompt: The user's prompt to process
        llm_model: The LLM model to use (openai, anthropic, gemini, or mock)
        visualize: Whether to generate visualization of the orchestration flow
        trace: Whether to enable detailed tracing of agent interactions
    """
    # Initialize state
    state = LangGraphState(
        user_input=prompt,
        current_step=OrchestrationStep.PLANNING,
        tasks=[],
        context={},
        state_id=None
    )
    
    # Initialize enhanced tracer if tracing is enabled
    tracer = EnhancedTraceLogger() if trace else None
    
    # Initialize LLM adapter with specified model
    try:
        llm_adapter = get_llm_adapter(provider=llm_model, fallback=False)
        print(f"\n[LLM] Using model: {llm_model}")
    except Exception as e:
        print(f"\n[LLM] Failed to initialize {llm_model} model: {e}")
        print("[LLM] Falling back to default model (openai)")
        llm_adapter = get_llm_adapter(provider="openai", fallback=True)
    
    # Add LLM adapter to state context
    state.context["llm_adapter"] = llm_adapter
    
    # Add tracer to state context if enabled
    if tracer:
        state.context["tracer"] = tracer
    
    # Build and run the graph
    graph = build_graph()
    
    try:
        print("\n[Orchestration] Starting execution...")
        # Run the orchestration
        start_time = datetime.now()
        final_state = await graph.ainvoke(state)
        
        # Patch: convert dict/AddableValuesDict to LangGraphState if needed
        if not isinstance(final_state, LangGraphState):
            try:
                final_state = LangGraphState(**dict(final_state))
            except Exception as e:
                print(f"\n[Error] Failed to convert final state: {e}")
                raise
        
        # Log results
        duration = (datetime.now() - start_time).total_seconds()
        print(f"\n[Orchestration] Completed in {duration:.2f} seconds")
        
        # Print full output of every completed task
        print("\n" + "=" * 50)
        print(" " * 15 + "TASK OUTPUTS (FULL)")
        print("=" * 50)
        for task in final_state.tasks:
            if hasattr(task, 'status') and getattr(task, 'status', None) == 'COMPLETED':
                print(f"\nTask: {task.task_id} | Agent: {getattr(task, 'assigned_agent', 'Unknown')} | Type: {getattr(task, 'task_type', 'Unknown')}")
                print(f"Description: {getattr(task, 'description', '')}")
                print("Output:")
                print(getattr(task, 'output', '[No output]'))
                print("-" * 40)
        print("=" * 50)
        
        # Print final summary with enhanced formatting
        if "final_summary" in final_state.context:
            summary = final_state.context["final_summary"]
            print("\n" + "=" * 50)
            print(" " * 15 + "ORCHESTRATION SUMMARY")
            print("=" * 50)
            
            # Task Statistics
            print("\n📊 Task Statistics:")
            print(f"  • Total Tasks: {summary['total_tasks']}")
            print(f"  • Completed: {summary['completed_tasks']}")
            print(f"  • Failed: {summary['failed_tasks']}")
            print(f"  • Success Rate: {summary['success_rate']:.2%}")
            print(f"  • Total Time: {summary['total_time']:.2f}s")
            
            # Agent Statistics
            if tracer and tracer.agent_states:
                print("\n👥 Agent Statistics:")
                for agent, states in tracer.agent_states.items():
                    print(f"  • {agent}: {len(states)} state changes")
            
            # Task Details
            if tracer and tracer.task_progress:
                print("\n📝 Task Details:")
                for task_id, updates in tracer.task_progress.items():
                    print(f"\n  Task: {task_id}")
                    for update in updates:
                        print(f"    [{update['timestamp']}] {update['status']} by {update['agent']}")
            
            # Agent Interactions
            if tracer and tracer.conversations:
                print("\n💬 Agent Interactions:")
                for conv in tracer.conversations:
                    print(f"\n  [{conv['timestamp']}] {conv['from_agent']} -> {conv['to_agent']}")
                    print(f"    Message: {conv['message']}")
            
            print("\n" + "=" * 50)
        
        # Print the final system response (output of the last completed task, or last CoCEO/summary/communication task)
        final_response = None
        completed_tasks = [t for t in final_state.tasks if getattr(t, 'status', None) == 'COMPLETED']
        if completed_tasks:
            # Prefer a task assigned to CoCEO, or of type COMMUNICATION/SUMMARY, else just the last completed
            coceo_tasks = [t for t in completed_tasks if getattr(t, 'assigned_agent', '').lower() == 'coceo']
            comm_tasks = [t for t in completed_tasks if getattr(t, 'task_type', '').upper() in ('COMMUNICATION', 'SUMMARY')]
            if coceo_tasks:
                final_response = coceo_tasks[-1].output
            elif comm_tasks:
                final_response = comm_tasks[-1].output
            else:
                final_response = completed_tasks[-1].output
        if final_response:
            print("\n" + "=" * 50)
            print(" " * 15 + "FINAL SYSTEM RESPONSE")
            print("=" * 50)
            print(final_response)
            print("\n" + "=" * 50)
        
        # Print task execution details
        if tracer and tracer.task_progress:
            print("\n=== Task Execution Details ===")
            for task_id, updates in tracer.task_progress.items():
                print(f"\nTask: {task_id}")
                for update in updates:
                    print(f"  [{update['timestamp']}] Status: {update['status']} by {update['agent']}")
                    if update['details']:
                        print(f"  Details: {json.dumps(update['details'], indent=2)}")
            print("=" * 30)
        
        # Print agent conversations if tracing was enabled
        if tracer and tracer.conversations:
            print("\n=== Agent Conversations ===")
            for conv in tracer.conversations:
                print(f"\n[{conv['timestamp']}] {conv['from_agent']} -> {conv['to_agent']}:")
                print(f"Message: {conv['message']}")
                if conv['context']:
                    print(f"Context: {json.dumps(conv['context'], indent=2)}")
            print("=" * 30)
        
        # Print agent state changes
        if tracer and tracer.agent_states:
            print("\n=== Agent State Changes ===")
            for agent, states in tracer.agent_states.items():
                print(f"\nAgent: {agent}")
                for state_update in states:
                    print(f"  [{state_update['timestamp']}]")
                    print(f"  State: {json.dumps(state_update['state'], indent=2)}")
            print("=" * 30)
            
    except Exception as e:
        print(f"\n[Error] Orchestration failed: {str(e)}")
        raise

def main(prompt: Optional[str] = None):
    parser = argparse.ArgumentParser(description="Test the orchestration system")
    parser.add_argument("--prompt", type=str, required=False, help="The prompt to process")
    parser.add_argument("--llm-model", type=str, default="openai", 
                      choices=["openai", "anthropic", "gemini", "mock"],
                      help="The LLM model to use")
    parser.add_argument("--no-visualize", action="store_true", help="Disable visualization")
    parser.add_argument("--no-trace", action="store_true", help="Disable detailed tracing")
    parser.add_argument("--log-level", type=str, default="INFO", 
                      choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                      help="Set the logging level")
    args = parser.parse_args()
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the orchestration
    asyncio.run(run_orchestration(
        args.prompt or prompt,
        args.llm_model,
        not args.no_visualize,
        not args.no_trace
    ))

if __name__ == "__main__":
    main() 