"""
LLM Adapter Base Classes and Types

This module defines the base classes and types for LLM adapters.
"""

import logging  # For logging metrics and errors
import time  # For tracking latency of LLM calls
from abc import ABC, abstractmethod  # To define the abstract LLMAdapter base class
from functools import wraps  # To preserve metadata when decorating methods
from typing import (  # Type hints for static analysis and better tooling support
    AsyncIterator,  # For streaming async responses from chat
    Dict,  # Generic mapping type (used in metrics or config)
    List,  # For lists of chat messages
    Literal,  # For fixed string values like 'user', 'assistant', 'system'
    Optional,  # For values that may be None
    Tuple,  # For fixed-length token count returns
    TypedDict,  # For defining structured dictionaries like ChatMessage and LLMConfig
    Union,  # For functions returning multiple possible types (e.g., string or stream)
)

logger = logging.getLogger(__name__)  # Module-level logger for capturing adapter metrics and errors


class ChatMessage(TypedDict):
    """
    Standard chat message format.

    Attributes:
        role: The role of the message sender (system, user, assistant)
        content: The content of the message
    """
    # Literal restricts role to strings 'user', 'assistant', or 'system'
    role: Literal["user", "assistant", "system"]
    content: str


class LLMConfig(TypedDict, total=False):
    """
    Configuration options for LLM adapters.

    This TypedDict centralizes configuration options for LLM adapters,
    making it easier to validate and document configuration parameters.
    The 'total=False' means all fields are optional.

    Attributes:
        api_key: API key for the LLM provider
        model: Model name to use
        temperature: Determnistic vs. creative output (0.0 to 1.0)
        max_tokens: Maximum tokens to generate
        timeout: Request timeout in seconds
        max_retries: Maximum number of retries
        streaming: Whether to stream the response
        base_url: Base URL for the API (for custom endpoints)
        api_version: API version to use
        organization: Organization ID (for OpenAI)
    """

    api_key: Optional[str]
    model: str
    temperature: float = 0.7
    max_tokens: Optional[int] = None
    timeout: float = 30.0
    max_retries: int = 3
    streaming: bool = False
    base_url: Optional[str] = None
    api_version: Optional[str] = None
    organization: Optional[str] = None


def log_llm_metrics(func):
    """
    Decorator to log LLM API call metrics.
    """

    @wraps(func)
    async def wrapper(self, *args, **kwargs):
        start_time = time.time()
        provider = self.__class__.__name__.replace("Adapter", "")

        # Extract attempt number if provided
        attempt = kwargs.get("attempt", 1)
        streaming = kwargs.get("stream", getattr(self, "streaming", False))

        # Get provider-specific config
        provider_config = {}
        for attr in ["base_url", "api_version", "organization"]:
            if hasattr(self, attr) and getattr(self, attr):
                provider_config[attr] = getattr(self, attr)

        try:
            result = await func(self, *args, **kwargs)

            # Calculate metrics
            elapsed_time = time.time() - start_time

            # Prepare metrics dictionary
            metrics = {
                "provider": provider,
                "model": getattr(self, "model", "unknown"),
                "latency_ms": round(elapsed_time * 1000, 2),
                "success": True,
                "attempt": attempt,
                "streaming": streaming,
            }

            # Add provider config if available
            if provider_config:
                metrics["provider_config"] = provider_config

            # Log success metrics
            logger.info(
                f"LLM API call completed: {provider}", extra={"metrics": metrics}
            )

            return result
        except Exception as e:
            # Calculate error metrics
            elapsed_time = time.time() - start_time

            # Prepare metrics dictionary
            metrics = {
                "provider": provider,
                "model": getattr(self, "model", "unknown"),
                "latency_ms": round(elapsed_time * 1000, 2),
                "success": False,
                "attempt": attempt,
                "streaming": streaming,
                "error_type": type(e).__name__,
                "error_message": str(e),
            }

            # Add provider config if available
            if provider_config:
                metrics["provider_config"] = provider_config

            # Log error metrics with traceback
            logger.exception(
                f"Error calling {provider} API: {str(e)}", extra={"metrics": metrics}
            )
            raise

    return wrapper


class LLMAdapter(ABC):
    """
    Abstract base class for LLM adapters.

    This class provides a unified interface for interacting with different LLM providers,
    with a focus on simplicity and consistency.
    """

    @abstractmethod
    async def chat(
        self,
        messages: List[ChatMessage],
        stream: bool = False,
        **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """
        Send a chat request to the LLM.
        
        Args:
            messages: List of chat messages
            stream: Whether to stream the response
            **kwargs: Additional provider-specific arguments
            
        Returns:
            Response as string or async iterator if streaming
        """
        pass

    @abstractmethod
    async def get_token_count(self, messages: List[ChatMessage]) -> Tuple[int, int]:
        """
        Count tokens in messages.
        
        Args:
            messages: List of chat messages
            
        Returns:
            Tuple of (prompt_tokens, completion_tokens)
        """
        pass

    @property
    @abstractmethod
    def supports_streaming(self) -> bool:
        """Whether the adapter supports streaming responses."""
        pass

    @property
    @abstractmethod
    def model(self) -> str:
        """The model name being used."""
        pass

    @property
    @abstractmethod
    def client(self):
        """Access to the underlying client instance."""
        pass

    def get_stream_flag(self, stream: bool = False) -> bool:
        """
        Get the appropriate stream flag based on adapter capabilities.

        Args:
            stream: Whether streaming was requested

        Returns:
            Boolean indicating whether to stream the response (True only if
            streaming is both requested and supported by the adapter)
        """
        # Only return True if streaming is both requested and supported
        return stream and self.supports_streaming

    async def _call_llm(self, prompt: str, **kwargs) -> str:
        """Call the LLM with the given prompt and return the response."""
        if not self._llm:
            raise RuntimeError("LLM not initialized")
        
        # Always use the real LLM response
        response = await self._llm.ainvoke(prompt, **kwargs)
        return response
