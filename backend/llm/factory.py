"""
LLM Factory

This module provides a factory for creating LLM adapters.
"""

import logging
from typing import Dict, Optional, Type

from .base import LLMAdapter, LLMConfig
from .openai import OpenAIAdapter
from .anthropic import AnthropicAdapter
from .gemini import GeminiAdapter
from .mock import MockAdapter

logger = logging.getLogger(__name__)


class LLMFactory:
    """
    Factory for creating LLM adapters.
    
    This class provides a centralized way to create and configure LLM adapters,
    with support for different providers and configurations.
    """

    _adapters: Dict[str, Type[LLMAdapter]] = {
        "openai": OpenAIAdapter,
        "anthropic": AnthropicAdapter,
        "gemini": GeminiAdapter,
        "mock": MockAdapter
    }

    @classmethod
    def create(
        cls,
        provider: str,
        config: Optional[LLMConfig] = None,
        **kwargs
    ) -> LLMAdapter:
        """
        Create an LLM adapter.
        
        Args:
            provider: LLM provider name
            config: Optional configuration
            **kwargs: Additional provider-specific arguments
            
        Returns:
            Configured LLM adapter
            
        Raises:
            ValueError: If provider is not supported
        """
        provider = provider.lower()
        if provider not in cls._adapters:
            raise ValueError(f"Unsupported LLM provider: {provider}")
            
        adapter_class = cls._adapters[provider]
        logger.info(f"Creating {provider} adapter")
        
        return adapter_class(config=config, **kwargs)

    @classmethod
    def register_adapter(
        cls,
        name: str,
        adapter_class: Type[LLMAdapter]
    ) -> None:
        """
        Register a new LLM adapter.
        
        Args:
            name: Provider name
            adapter_class: Adapter class
        """
        cls._adapters[name.lower()] = adapter_class
        logger.info(f"Registered adapter: {name}")

    @classmethod
    def get_supported_providers(cls) -> list[str]:
        """Get list of supported LLM providers."""
        return list(cls._adapters.keys())
