"""
Centralized configuration for LLM adapters, including timeout and retry settings.
"""
from typing import Optional

class LLMConfig:
    def __init__(
        self,
        timeout: float = 30.0,
        max_retries: int = 3,
        min_backoff: int = 2,
        max_backoff: int = 10,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        streaming: bool = False,
    ):
        self.timeout = timeout
        self.max_retries = max_retries
        self.min_backoff = min_backoff
        self.max_backoff = max_backoff
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.streaming = streaming 