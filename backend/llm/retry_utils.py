"""
Shared retry and timeout utilities for LLM adapters.
Supports both Tenacity-based and manual retry logic with exponential backoff.
"""
import asyncio
import logging
from typing import Callable, Tuple, Type, Any, Optional

logger = logging.getLogger(__name__)

# Try to import Tenacity for robust retry logic
try:
    from tenacity import (
        retry,
        retry_if_exception_type,
        stop_after_attempt,
        wait_exponential
    )
    TENACITY_AVAILABLE = True
except ImportError:
    TENACITY_AVAILABLE = False
    # Dummy decorators for fallback
    def retry(*_args, **_kwargs):
        def decorator(func):
            return func
        return decorator
    def retry_if_exception_type(*_args, **_kwargs):
        return None
    def stop_after_attempt(*_args, **_kwargs):
        return None
    def wait_exponential(*_args, **_kwargs):
        return None


def llm_retry_async(
    func: Callable,
    exception_types: Tuple[Type[BaseException], ...],
    max_retries: int = 3,
    min_backoff: int = 2,
    max_backoff: int = 10,
    logger_prefix: Optional[str] = None,
    is_rate_limit_error: Optional[Callable[[Exception], bool]] = None,
) -> Callable:
    """
    Returns an async function with retry logic (Tenacity if available, else manual fallback).
    """
    async def wrapper(*args, **kwargs):
        prefix = logger_prefix or "LLM"
        if TENACITY_AVAILABLE:
            @retry(
                stop=stop_after_attempt(max_retries),
                wait=wait_exponential(multiplier=1, min=min_backoff, max=max_backoff),
                retry=retry_if_exception_type(exception_types),
                reraise=True,
            )
            async def _call():
                return await func(*args, **kwargs)
            return await _call()
        else:
            attempt = 0
            last_exception = None
            while attempt < max_retries:
                try:
                    return await func(*args, **kwargs)
                except exception_types as e:
                    attempt += 1
                    last_exception = e
                    if attempt >= max_retries:
                        break
                    error_message = str(e).lower()
                    is_rate = is_rate_limit_error(e) if is_rate_limit_error else False
                    wait_time = min(max_backoff, min_backoff * (2 ** attempt))
                    if is_rate:
                        logger.warning(f"{prefix}: Rate limit hit. Retry {attempt}/{max_retries}. Waiting {wait_time}s...")
                    else:
                        logger.warning(f"{prefix}: Retry {attempt}/{max_retries} after error: {e}. Waiting {wait_time}s...")
                    await asyncio.sleep(wait_time)
                except Exception as e:
                    raise
            if last_exception:
                logger.error(f"{prefix}: Max retries ({max_retries}) reached.", exc_info=last_exception)
                raise last_exception
    return wrapper 