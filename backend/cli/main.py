#!/usr/bin/env python3
"""
BusinessLM CLI

Unified command-line interface for BusinessLM orchestration system.
Provides access to database, document, testing, and configuration operations.
"""

import sys
import argparse
import asyncio
from pathlib import Path


# Add backend to path for imports
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

from cli.commands.database import setup_database_parser, handle_database_command
from cli.commands.documents import setup_documents_parser, handle_documents_command
from cli.commands.test import setup_test_parser, handle_test_command
from cli.commands.config import setup_config_parser, handle_config_command
from cli.utils import print_header, print_error

from backend.llm.factory import get_llm_adapter
from backend.orchestration.state import LangGraphState, OrchestrationStep, TaskStatus
from backend.orchestration.graph import build_graph


async def create_initial_state(user_input: str) -> LangGraphState:
    """Create the initial state for the orchestration."""
    llm_adapter = get_llm_adapter()
    return LangGraphState(
        user_input=user_input,
        current_step=OrchestrationStep.PLANNING,
        tasks=[],
        context={"llm_adapter": llm_adapter}
    )

def create_parser():
    """Create the main CLI parser."""
    parser = argparse.ArgumentParser(
        description='BusinessLM Orchestration CLI',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s database setup --create-db
  %(prog)s database migrate upgrade
  %(prog)s documents load --source ./docs
  %(prog)s test rag --mode full
  %(prog)s test system --mode clean
  %(prog)s config validate
  %(prog)s orchestration run --user-input "What is the current financial performance of the company?"
        """
    )
    
    parser.add_argument('--version', action='version', version='BusinessLM CLI 1.0.0')
    
    # Create subparsers for main commands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Set up command parsers
    setup_database_parser(subparsers)
    setup_documents_parser(subparsers)
    setup_test_parser(subparsers)
    setup_config_parser(subparsers)

    # Add orchestration parent parser
    orchestration_parser = subparsers.add_parser('orchestration', help='Orchestration workflows')
    orchestration_subparsers = orchestration_parser.add_subparsers(dest='orchestration_command')

    # Add orchestration subcommand
    run_parser = orchestration_subparsers.add_parser('run', help='Run orchestration workflow')
    run_parser.add_argument('query', type=str, help='User input or query for the workflow')

    return parser


async def handle_run_command(args):
    """Handle the 'run' command to start an orchestration workflow."""
    try:
        state = await create_initial_state(args.query)
        graph = build_graph()
        final_state = await graph.ainvoke(state)
        
        print("\n=== Orchestration Completed ===")
        print(f"Final Step: {final_state.current_step.value}")
        print(f"Completed Tasks: {len(final_state.get_completed_tasks())}")
        print(f"Failed Tasks: {len([t for t in final_state.tasks if t.status == TaskStatus.FAILED])}")
        
        if "final_summary" in final_state.context:
            print("\nSummary:")
            for k, v in final_state.context["final_summary"].items():
                print(f"- {k}: {v}")
        else:
            print("\nNo summary generated (orchestration may have failed)")
        
        return 0
    except Exception as e:
        print_error(f"Orchestration failed: {e}")
        return 1


def main():
    """Main CLI entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        print_header("BusinessLM Orchestration CLI")
        parser.print_help()
        return 0
    
    try:
        # Route to appropriate command handler
        if args.command == 'database':
            return handle_database_command(args)
        elif args.command == 'documents':
            return handle_documents_command(args)
        elif args.command == 'test':
            return handle_test_command(args)
        elif args.command == 'config':
            return handle_config_command(args)
        elif args.command == 'orchestration':
            if args.orchestration_command == 'run':
                return asyncio.run(handle_run_command(args))
            else:
                print_error(f"Unknown orchestration command: {args.orchestration_command}")
                return 1
        else:
            print_error(f"Unknown command: {args.command}")
            return 1
            
    except KeyboardInterrupt:
        print_error("Operation cancelled by user")
        return 1
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
