"""
The coordinator defines the logic for progressing through the orchestration steps by:
  - Assigning agents to available tasks.
  - Updating task statuses as defined in the state.py `TaskStatus` (`IN_PROGRESS`, `COMPLETED`, etc.).
  - Determining the next `OrchestrationStep` as defined in the state.py `OrchestrationStep` (`PLANNING`, `TASK_ASSIGNMENT`, `TASK_EXECUTION`, `REVIEW`, `DONE`).

Makes use of:
  - `get_task_by_id`, `task.status`, and `task.dependencies` from the state.py `LangGraphState`.
  - Routing decisions based on orchestration state.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Type, List, Optional

from ..agents.co_ceo import CoCEOAgent
from ..agents.marketing import MarketingAgent
from ..agents.finance import FinanceAgent
from ..agents.base import BaseAgent
from ..llm.base import LLMAdapter
from ..llm.factory import get_llm_adapter
from ..rag import KnowledgeBaseService, initialize_knowledge_base_service
from .state import LangGraphState, OrchestrationStep, TaskStatus, TaskType, Task
from .planner import plan_with_state
from .error_handling import handle_orchestration_errors
from .db import save_state, save_task
from .memory import GlobalMemory

logger = logging.getLogger(__name__)

# Optional: Make this external later
AGENT_REGISTRY: Dict[str, Type[BaseAgent]] = {
    "CoCEO": CoCEOAgent,
    "Finance": FinanceAgent,
    "Marketing": MarketingAgent,
}

class OrchestrationCoordinator:
    """
    The orchestration coordinator is responsible for 
    assigning agents to tasks and updating task statuses.
    """

    def __init__(self):
        self.step_handlers = {
            OrchestrationStep.PLANNING: self.coordinate_planning,
            OrchestrationStep.TASK_ASSIGNMENT: self.coordinate_task_assignment,
            OrchestrationStep.TASK_EXECUTION: self.coordinate_task_execution,
            OrchestrationStep.REVIEW: self.coordinate_review,
            OrchestrationStep.DONE: self.coordinate_done,
        }

        # Service instances
        self._llm_adapter: Optional[LLMAdapter] = None
        self._knowledge_base: Optional[KnowledgeBaseService] = None
        self._memory: Optional[GlobalMemory] = None

        # Agent instances cache
        self._agent_instances: Dict[str, BaseAgent] = {}

        # Progress tracking
        self.execution_metrics = {
            "start_time": None,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "total_tasks": 0,
            "task_times": {}
        }

    async def initialize_services(self) -> None:
        """Initialize required services."""
        try:
            # Initialize LLM adapter
            if not self._llm_adapter:
                self._llm_adapter = get_llm_adapter()
                logger.info("Initialized LLM adapter")

            # Initialize knowledge base
            if not self._knowledge_base:
                self._knowledge_base = await initialize_knowledge_base_service()
                logger.info("Initialized knowledge base service")

            # Initialize memory
            if not self._memory:
                self._memory = GlobalMemory(knowledge_base=self._knowledge_base)
                logger.info("Initialized global memory")

        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            raise

    def get_agent_instance(self, agent_name: str) -> BaseAgent:
        """Get or create an agent instance based on registry."""
        if not self._llm_adapter:
            raise ValueError("LLM adapter not initialized")

        if agent_name not in self._agent_instances:
            agent_cls = AGENT_REGISTRY.get(agent_name)
            if not agent_cls:
                raise ValueError(f"Unknown agent type: {agent_name}")
            
            # Create agent instance with services
            self._agent_instances[agent_name] = agent_cls(
                llm_adapter=self._llm_adapter,
                memory=self._memory,
                knowledge_base=self._knowledge_base
            )
            logger.info(f"Created agent instance: {agent_name}")
            
        return self._agent_instances[agent_name]

    async def execute_task(self, task: Task, state: LangGraphState) -> None:
        """Execute a single task and update its status."""
        start_time = datetime.now()
        try:
            # Ensure services are initialized
            await self.initialize_services()
            
            # Get agent instance
            agent = self.get_agent_instance(task.assigned_agent)
            logger.info(f"[EXECUTION] Executing task {task.task_id} ({task.description}) with agent {task.assigned_agent}")
            
            # Execute task with timeout
            timeout = task.metadata.get("timeout", 60)  # seconds
            task.output = await asyncio.wait_for(
                agent.run(task, state),
                timeout=timeout
            )
            logger.info(f"[EXECUTION] Agent {task.assigned_agent} output: {task.output}")
            task.status = TaskStatus.COMPLETED

            # Track agent interactions
            if not task.metadata.get("conversations"):
                task.metadata["conversations"] = []

            # Add the conversation to the task
            task.metadata["conversations"].append({
                "from_agent": task.assigned_agent,
                "to_agent": "user",
                "message": task.output,
                "timestamp": datetime.now().isoformat()
            })

            # Track in global state
            if "agent_interactions" not in state.context:
                state.context["agent_interactions"] = []

            state.context["agent_interactions"].append({
                "from_agent": task.assigned_agent,
                "to_agent": "user",
                "type": "task_completion",
                "message": task.output,
                "timestamp": datetime.now().isoformat()
            })

            # Post-process task (future hook for memory/tools)
            await self.post_process_task(task, state)

            # Update metrics
            self.execution_metrics["completed_tasks"] += 1
            self.execution_metrics["task_times"][task.task_id] = {
                "start": start_time,
                "end": datetime.now(),
                "duration": (datetime.now() - start_time).total_seconds()
            }

        except asyncio.TimeoutError:
            logger.error(f"Task {task.task_id} timed out after {timeout} seconds")
            task.status = TaskStatus.FAILED
            task.output = f"Task timed out after {timeout} seconds"
            self.execution_metrics["failed_tasks"] += 1

            # Track failed task interaction
            if "agent_interactions" not in state.context:
                state.context["agent_interactions"] = []
            state.context["agent_interactions"].append({
                "from_agent": task.assigned_agent,
                "to_agent": "system",
                "type": "task_failure",
                "message": f"Task timed out after {timeout} seconds",
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"Error executing task {task.task_id}: {str(e)}")
            task.status = TaskStatus.FAILED
            task.output = f"Task failed: {str(e)}"
            self.execution_metrics["failed_tasks"] += 1

            # Track error interaction
            if "agent_interactions" not in state.context:
                state.context["agent_interactions"] = []
            state.context["agent_interactions"].append({
                "from_agent": task.assigned_agent,
                "to_agent": "system",
                "type": "task_error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            })

    async def post_process_task(self, task: Task, state: LangGraphState) -> None:
        """
        Post-process a completed task.
        This is a future hook for:
        - Memory management
        - Tool cleanup
        - Result caching
        - Analytics
        """
        pass

    @handle_orchestration_errors("OrchestrationCoordinator")
    async def coordinate_planning(self, state: LangGraphState) -> LangGraphState:
        """
        Plan tasks based on user input and update state.
        """
        # Ensure services are initialized
        await self.initialize_services()
        
        # Get CoCEO agent for planning
        co_ceo = self.get_agent_instance("CoCEO")
        
        try:
            # Generate plan
            tasks, analysis = await co_ceo.plan_execution(
                user_input=state.context.get("user_input", ""),
                context=state.context
            )
            
            # Update state with plan
            state.tasks = tasks
            state.context["plan_analysis"] = analysis
            
            # Move to next step
            state.current_step = OrchestrationStep.TASK_ASSIGNMENT
            
            return state
            
        except Exception as e:
            logger.error(f"Planning failed: {e}")
            state.context["error"] = str(e)
            state.current_step = OrchestrationStep.DONE
            return state

    @handle_orchestration_errors("OrchestrationCoordinator")
    async def coordinate_task_assignment(self, state: LangGraphState) -> LangGraphState:
        """
        Assign agents to tasks and update state.
        This method:
        1. Identifies unassigned tasks
        2. Assigns appropriate agents based on task type
        3. Updates task metadata
        4. Transitions to TASK_EXECUTION
        """
        logger.info("Starting task assignment phase")

        # Get unassigned tasks
        unassigned_tasks = [task for task in state.tasks if not task.assigned_agent]

        # Assign agents to tasks
        for task in unassigned_tasks:
            task.assigned_agent = self.assign_agent(task)
            task.status = TaskStatus.PENDING
            logger.info(f"Assigned {task.assigned_agent} to task {task.task_id}")
        
        # Move to task execution
        state.current_step = OrchestrationStep.TASK_EXECUTION
        return state

    def assign_agent(self, task: Task) -> str:
        """
        Assign an agent to a task based on task type and requirements.
        This method:
        1. Analyzes task type and requirements
        2. Selects the most appropriate agent
        3. Returns the agent name
        """
        # Map task types to agents
        task_type_to_agent = {
            TaskType.STRATEGIC: "CoCEO",
            TaskType.FINANCIAL: "Finance",
            TaskType.MARKETING: "Marketing",
            TaskType.ANALYSIS: "CoCEO",  # Default to CoCEO for analysis tasks
        }
        
        # Get agent based on task type
        agent = task_type_to_agent.get(task.task_type, "CoCEO")
        
        # Log assignment
        logger.info(f"Assigned {agent} to task {task.task_id} of type {task.task_type}")
        
        return agent

    @handle_orchestration_errors("OrchestrationCoordinator")
    async def coordinate_task_execution(self, state: LangGraphState) -> LangGraphState:
        """
        Execute tasks and update state.
        This method:
        1. Identifies tasks ready for execution (dependencies completed)
        2. Executes tasks in parallel where possible
        3. Updates task statuses and state
        4. Transitions to REVIEW when all tasks are complete
        """
        # Initialize metrics if not already done
        if self.execution_metrics["start_time"] is None:
            self.execution_metrics["start_time"] = datetime.now()
            self.execution_metrics["total_tasks"] = len(state.tasks)
        
        # Get tasks that are ready to execute
        ready_tasks = [
            task for task in state.tasks
            if task.status == TaskStatus.PENDING
            and all(
                (dep := state.get_task_by_id(dep_id)) and dep.status == TaskStatus.COMPLETED
                for dep_id in task.dependencies
            )
        ]
        
        if not ready_tasks:
            # If no tasks are ready but we have pending tasks, we're waiting for dependencies
            if any(task.status == TaskStatus.PENDING for task in state.tasks):
                return state
            
            # If all tasks are complete, move to review
            if all(task.status in {TaskStatus.COMPLETED, TaskStatus.FAILED} for task in state.tasks):
                # Log final metrics
                total_time = (datetime.now() - self.execution_metrics["start_time"]).total_seconds()
                logger.info(
                    "Task execution completed:\n"
                    f"Total tasks: {self.execution_metrics['total_tasks']}\n"
                    f"Completed: {self.execution_metrics['completed_tasks']}\n"
                    f"Failed: {self.execution_metrics['failed_tasks']}\n"
                    f"Total time: {total_time:.2f}s"
                )
                state.current_step = OrchestrationStep.REVIEW
            return state
        
        # Execute ready tasks in parallel
        await asyncio.gather(*[self.execute_task(task, state) for task in ready_tasks])
        
        return state

    @handle_orchestration_errors("OrchestrationCoordinator")
    async def coordinate_review(self, state: LangGraphState) -> LangGraphState:
        """
        Review tasks and update state.
        This method:
        1. Reviews all completed tasks
        2. Identifies any failed tasks that need attention
        3. Determines if additional tasks are needed
        4. Transitions to DONE if everything is satisfactory
        """
        # Get all completed and failed tasks
        completed_tasks = state.get_completed_tasks()
        failed_tasks = [task for task in state.tasks if task.status == TaskStatus.FAILED]
        
        # Review failed tasks first
        if failed_tasks:
            logger.warning(f"Found {len(failed_tasks)} failed tasks during review")
            
            # Retry failed tasks
            for task in failed_tasks:
                retry_count = task.metadata.get("retry_count", 0)
                max_retries = task.metadata.get("max_retries", 3)
                
                if retry_count < max_retries:
                    logger.info(f"Retrying task {task.task_id} (attempt {retry_count + 1}/{max_retries})")
                    task.status = TaskStatus.PENDING
                    task.metadata["retry_count"] = retry_count + 1
                    state.current_step = OrchestrationStep.TASK_EXECUTION
                    return state
                else:
                    logger.error(f"Task {task.task_id} failed after {max_retries} attempts")
        
        # Generate final summary
        summary = {
            "total_tasks": len(state.tasks),
            "completed_tasks": len(completed_tasks),
            "failed_tasks": len(failed_tasks),
            "success_rate": len(completed_tasks) / len(state.tasks) if state.tasks else 0,
            "total_time": (datetime.now() - self.execution_metrics["start_time"]).total_seconds() if self.execution_metrics["start_time"] else 0
        }
        
        # Add summary to state context
        state.context["final_summary"] = summary
        
        # Move to done
        state.current_step = OrchestrationStep.DONE
        return state

    @handle_orchestration_errors("OrchestrationCoordinator")
    async def coordinate_done(self, state: LangGraphState) -> LangGraphState:
        """
        Finalize orchestration and update state.
        This method:
        1. Persists final state
        2. Generates final report
        3. Cleans up resources
        """
        logger.info("Finalizing orchestration")
        # Clean non-serializable objects from context before saving
        keys_to_remove = [k for k in state.context if k in ("llm_adapter", "tracer")]
        for k in keys_to_remove:
            state.context.pop(k, None)
        # Save final state
        await save_state(state)
        # Log completion
        logger.info("Orchestration completed successfully")
        return state
