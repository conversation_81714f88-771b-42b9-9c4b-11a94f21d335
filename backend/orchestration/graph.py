"""
Defines the node map of orchestration:
  - Each `OrchestrationStep` becomes a LangGraph node.
  - Transitions governed by `state.current_step`.

Adds support for dynamic state evolution and branching logic.
Attaches tracing to observe node inputs/outputs.
"""

import logging
from langgraph.graph import StateGraph, START, END
from langchain_core.runnables import <PERSON><PERSON><PERSON>
from typing import Optional

from backend.llm.factory import get_llm_adapter
from .state import LangGraphState
from .coordinator import OrchestrationCoordinator
from .error_handling import handle_orchestration_errors

logger = logging.getLogger(__name__)

def ensure_llm_adapter(node_func):
    """
    Ensure the LLM adapter is available in the state context.
    If not, initialize it and add it to the context before calling the node function.
    """
    async def wrapper(state: LangGraphState) -> LangGraphState:
        if "llm_adapter" not in state.context:
            state.context["llm_adapter"] = get_llm_adapter()
        return await node_func(state)
    return wrapper

def build_graph() -> Runnable[LangGraphState, LangGraphState]:
    """
    Build the orchestration graph.

    The graph defines the flow of orchestration:
    1. PLANNING: Initial task planning and decomposition
    2. TASK_ASSIGNMENT: Assigning agents to tasks
    3. TASK_EXECUTION: Executing tasks in parallel
    4. REVIEW: Reviewing results and handling failures
    5. DONE: Finalizing and persisting results

    Each node is a coordinator method that:
    - Processes the current state
    - Updates task statuses
    - Determines the next step
    - Returns the updated state

    The graph uses conditional routing based on state.current_step
    to handle dynamic flow control.
    """
    coordinator = OrchestrationCoordinator()
    graph = StateGraph(LangGraphState)


    def route_step(state: LangGraphState) -> str:
        """
        Route the state to the appropriate node based on current_step.
        Handles both enum and string values for current_step.
        Returns lowercase node names.
        """
        import sys; sys.stdout.flush()
        if hasattr(state.current_step, 'value'):
            current_step = state.current_step.value
        else:
            current_step = state.current_step
        current_step = str(current_step).lower()
        logger.debug(f"Routing state to node: {current_step} (type: {type(state.current_step)})")
        if state.context.get("error_state"):
            logger.warning("Error state detected, routing to review")
            return "review"
        return current_step


    # Add nodes for each orchestration step
    # Each node is wrapped with error handling and logging
    # Planning node is additionally wrapped with LLM adapter initialization
    graph.add_node(
        "planning",
        handle_orchestration_errors("planning")(
            ensure_llm_adapter(coordinator.coordinate_planning)
        )
    )
    graph.add_node(
        "task_assignment",
        handle_orchestration_errors("task_assignment")(coordinator.coordinate_task_assignment)
    )
    graph.add_node(
        "task_execution",
        handle_orchestration_errors("task_execution")(coordinator.coordinate_task_execution)
    )
    graph.add_node(
        "review",
        handle_orchestration_errors("review")(coordinator.coordinate_review)
    )
    graph.add_node(
        "done",
        handle_orchestration_errors("done")(coordinator.coordinate_done)
    )

    # Connect start and end nodes
    graph.add_edge(START, "planning")
    graph.add_edge("planning", "task_assignment")
    graph.add_edge("task_assignment", "task_execution")
    graph.add_edge("task_execution", "review")
    graph.add_edge("review", "done")
    graph.add_edge("done", END)

    # Add conditional edges for review node
    # This allows dynamic routing based on state.current_step after review
    graph.add_conditional_edges(
        "review",
        route_step,
        {
            "planning": "planning",    # Return to planning if new tasks needed
            "task_execution": "task_execution",  # Return to execution for retries
            "done": "done",            # Proceed to completion
        }
    )

    # Set the entry point to planning
    graph.set_entry_point("planning")

    # Compile the graph into a callable function
    return graph.compile()
